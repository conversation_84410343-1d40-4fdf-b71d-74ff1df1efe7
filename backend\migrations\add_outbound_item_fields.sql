-- 给销售出库单明细表添加行号字段和已退货数量字段
-- 执行前请备份数据库

-- 1. 添加行号字段
ALTER TABLE sales_outbound_items ADD COLUMN line_number INTEGER NOT NULL DEFAULT 1;

-- 2. 添加已退货数量字段
ALTER TABLE sales_outbound_items ADD COLUMN returned_quantity INTEGER NOT NULL DEFAULT 0;

-- 3. 为现有数据设置行号（按创建时间排序）
-- 使用窗口函数为每个出库单的明细分配行号
UPDATE sales_outbound_items 
SET line_number = (
    SELECT row_number() OVER (PARTITION BY outbound_id ORDER BY created_at, id)
    FROM (
        SELECT id, outbound_id, created_at,
               row_number() OVER (PARTITION BY outbound_id ORDER BY created_at, id) as rn
        FROM sales_outbound_items
    ) ranked
    WHERE ranked.id = sales_outbound_items.id
);

-- 4. 创建索引以提高查询性能
CREATE INDEX idx_sales_outbound_items_line_number ON sales_outbound_items(outbound_id, line_number);
CREATE INDEX idx_sales_outbound_items_returned_qty ON sales_outbound_items(returned_quantity);

-- 5. 添加约束确保行号在同一出库单内唯一
ALTER TABLE sales_outbound_items 
ADD CONSTRAINT uk_outbound_line_number 
UNIQUE (outbound_id, line_number);

-- 6. 添加检查约束确保已退货数量不为负数且不超过实出数量
ALTER TABLE sales_outbound_items 
ADD CONSTRAINT chk_returned_quantity_valid 
CHECK (returned_quantity >= 0 AND returned_quantity <= outbound_quantity);

-- 注意事项：
-- 1. 执行前请备份数据库
-- 2. 行号字段用于内部管理，前端不展示
-- 3. 已退货数量字段用于跟踪退货情况，前端不展示
-- 4. 这些字段主要用于后台数据管理和业务逻辑处理
-- 5. 建议在维护窗口期间执行
