"""
销售管理服务 - 包含销售订单、销售出库单
"""

from typing import List, Tuple, Optional
from datetime import datetime
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, func, desc
from app.models.sales import (
    SalesOrder, SalesOrderItem,
    SalesOutbound, SalesOutboundItem
)
from app.models.customer import Customer
from app.models.product import Product
from app.models.warehouse import Warehouse
from app.schemas.sales import (
    SalesOrderCreate, SalesOrderUpdate, SalesOrderQuery, SalesOrderStats,
    SalesOutboundCreate, SalesOutboundStatus, SalesOrderStatus
)


class SalesService:
    """销售管理服务 - 包含销售订单、销售出库单"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_sales_orders(
        self, 
        skip: int = 0, 
        limit: int = 20, 
        query: Optional[SalesOrderQuery] = None
    ) -> Tuple[List[SalesOrder], int]:
        """获取销售订单列表"""
        
        # 构建查询
        db_query = self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items).joinedload(SalesOrderItem.product)
        )
        
        # 应用筛选条件
        if query:
            if query.order_no:
                db_query = db_query.filter(SalesOrder.order_no.ilike(f"%{query.order_no}%"))
            if query.customer_id:
                db_query = db_query.filter(SalesOrder.customer_id == query.customer_id)
            if query.status:
                db_query = db_query.filter(SalesOrder.status == query.status)
            if query.sales_person:
                db_query = db_query.filter(SalesOrder.sales_person.ilike(f"%{query.sales_person}%"))
            if query.platform:
                db_query = db_query.filter(SalesOrder.platform == query.platform)
            if query.start_date:
                db_query = db_query.filter(SalesOrder.created_at >= query.start_date)
            if query.end_date:
                db_query = db_query.filter(SalesOrder.created_at <= query.end_date)
        
        # 获取总数
        total = db_query.count()
        
        # 分页和排序
        orders = db_query.order_by(desc(SalesOrder.created_at)).offset(skip).limit(limit).all()
        
        return orders, total
    
    def get_sales_order(self, order_id: int) -> Optional[SalesOrder]:
        """获取单个销售订单"""
        return self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items).joinedload(SalesOrderItem.product)
        ).filter(SalesOrder.id == order_id).first()
    
    def create_sales_order(self, order_data: SalesOrderCreate) -> SalesOrder:
        """创建销售订单"""
        
        # 生成订单号
        order_no = self._generate_order_no()
        
        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {order_data.customer_id} 不存在")
        
        # 创建订单
        db_order = SalesOrder(
            order_no=order_no,
            customer_id=order_data.customer_id,
            total_amount=order_data.total_amount,
            status=order_data.status,
            delivery_date=order_data.delivery_date,
            delivery_address=order_data.delivery_address,
            sales_person=order_data.sales_person,
            platform=order_data.platform,
            original_order_no=order_data.original_order_no,
            remark=order_data.remark
        )
        
        self.db.add(db_order)
        self.db.flush()  # 获取订单ID
        
        # 创建订单明细
        for item_data in order_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建订单明细
            db_item = SalesOrderItem(
                order_id=db_order.id,
                line_number=item_data.line_number,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                total_price=item_data.total_price
            )
            self.db.add(db_item)
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def update_sales_order(self, order_id: int, order_data: SalesOrderUpdate) -> Optional[SalesOrder]:
        """更新销售订单（仅更新基本信息，不包括明细）"""

        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None

        # 更新字段
        update_data = order_data.dict(exclude_unset=True)
        for field, value in update_data.items():
            setattr(db_order, field, value)

        db_order.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_order)

        return db_order

    def update_sales_order_with_items(self, order_id: int, order_data: SalesOrderCreate) -> Optional[SalesOrder]:
        """更新销售订单（包括明细）"""

        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None

        # 检查订单状态，只有草稿状态的订单才能修改明细
        if db_order.status not in ["draft"]:
            raise ValueError("只有草稿状态的订单才能修改明细")

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == order_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {order_data.customer_id} 不存在")

        # 更新订单基本信息
        db_order.customer_id = order_data.customer_id
        db_order.total_amount = order_data.total_amount
        db_order.status = order_data.status
        db_order.delivery_date = order_data.delivery_date
        db_order.delivery_address = order_data.delivery_address
        db_order.sales_person = order_data.sales_person
        db_order.platform = order_data.platform
        db_order.original_order_no = order_data.original_order_no
        db_order.remark = order_data.remark
        db_order.updated_at = datetime.now()

        # 删除原有明细
        self.db.query(SalesOrderItem).filter(SalesOrderItem.order_id == order_id).delete()

        # 创建新的订单明细
        for item_data in order_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 创建订单明细
            db_item = SalesOrderItem(
                order_id=db_order.id,
                line_number=item_data.line_number,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                quantity=item_data.quantity,
                unit_price=item_data.unit_price,
                total_price=item_data.total_price
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_order)

        return db_order
    
    def delete_sales_order(self, order_id: int) -> bool:
        """删除销售订单"""
        
        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return False
        
        # 检查订单状态，只有草稿状态的订单才能删除
        if db_order.status not in ["draft"]:
            raise ValueError("只有草稿状态的订单才能删除")
        
        self.db.delete(db_order)
        self.db.commit()
        
        return True
    
    def update_order_status(self, order_id: int, status: str) -> Optional[SalesOrder]:
        """更新订单状态"""
        
        db_order = self.db.query(SalesOrder).filter(SalesOrder.id == order_id).first()
        if not db_order:
            return None
        
        db_order.status = status
        db_order.updated_at = datetime.now()
        
        self.db.commit()
        self.db.refresh(db_order)
        
        return db_order
    
    def get_sales_stats(self) -> SalesOrderStats:
        """获取销售订单统计"""

        # 基础统计
        total_orders = self.db.query(SalesOrder).count()

        # 按状态统计
        draft_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "draft").count()
        submitted_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "submitted").count()
        approved_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "approved").count()
        rejected_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "rejected").count()
        processing_orders = approved_orders  # 处理中状态对应已审核状态
        shipped_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "shipped").count()
        cancelled_orders = self.db.query(SalesOrder).filter(SalesOrder.status == "cancelled").count()

        # 金额统计
        total_amount_result = self.db.query(func.sum(SalesOrder.total_amount)).filter(
            SalesOrder.status != "cancelled"
        ).scalar()
        total_amount = total_amount_result or 0

        # 平均订单金额
        average_order_amount = total_amount / max(1, total_orders - cancelled_orders)

        return SalesOrderStats(
            total_orders=total_orders,
            draft_orders=draft_orders,
            submitted_orders=submitted_orders,
            approved_orders=approved_orders,
            rejected_orders=rejected_orders,
            processing_orders=processing_orders,
            shipped_orders=shipped_orders,
            cancelled_orders=cancelled_orders,
            total_amount=total_amount,
            average_order_amount=average_order_amount
        )
    
    def _generate_order_no(self) -> str:
        """生成订单号"""
        today = datetime.now().strftime("%Y%m%d")
        
        # 查询今天已有的订单数量
        count = self.db.query(SalesOrder).filter(
            SalesOrder.order_no.like(f"SO{today}%")
        ).count()
        
        # 生成新的订单号
        sequence = str(count + 1).zfill(4)
        return f"SO{today}{sequence}"

    # ==================== 销售出库单相关方法 ====================

    def get_sales_outbounds(
        self,
        skip: int = 0,
        limit: int = 20,
        outbound_no: Optional[str] = None,
        sales_order_no: Optional[str] = None,
        customer_id: Optional[int] = None,
        warehouse_id: Optional[int] = None,
        status: Optional[str] = None,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None
    ) -> Tuple[List[SalesOutbound], int]:
        """获取销售出库单列表"""

        # 构建查询
        db_query = self.db.query(SalesOutbound).options(
            joinedload(SalesOutbound.customer),
            joinedload(SalesOutbound.sales_order),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.product),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.warehouse)
        )

        # 应用筛选条件
        if outbound_no and sales_order_no and outbound_no == sales_order_no:
            # 如果出库单号和销售订单号相同，说明是合并搜索，使用OR查询
            db_query = db_query.outerjoin(SalesOrder).filter(
                or_(
                    SalesOutbound.outbound_no.ilike(f"%{outbound_no}%"),
                    SalesOrder.order_no.ilike(f"%{sales_order_no}%")
                )
            )
        else:
            # 分别搜索
            if outbound_no:
                db_query = db_query.filter(SalesOutbound.outbound_no.ilike(f"%{outbound_no}%"))
            if sales_order_no:
                db_query = db_query.join(SalesOrder).filter(SalesOrder.order_no.ilike(f"%{sales_order_no}%"))
        if customer_id:
            db_query = db_query.filter(SalesOutbound.customer_id == customer_id)
        if warehouse_id:
            db_query = db_query.join(SalesOutboundItem).filter(SalesOutboundItem.warehouse_id == warehouse_id)
        if status:
            db_query = db_query.filter(SalesOutbound.status == status)
        if start_date:
            db_query = db_query.filter(SalesOutbound.created_at >= start_date)
        if end_date:
            db_query = db_query.filter(SalesOutbound.created_at <= end_date)

        # 获取总数
        total = db_query.count()

        # 分页和排序
        outbounds = db_query.order_by(desc(SalesOutbound.created_at)).offset(skip).limit(limit).all()

        return outbounds, total

    def get_sales_outbound(self, outbound_id: int) -> Optional[SalesOutbound]:
        """获取单个销售出库单"""
        return self.db.query(SalesOutbound).options(
            joinedload(SalesOutbound.customer),
            joinedload(SalesOutbound.sales_order),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.product),
            joinedload(SalesOutbound.items).joinedload(SalesOutboundItem.warehouse)
        ).filter(SalesOutbound.id == outbound_id).first()

    def create_sales_outbound(self, outbound_data: SalesOutboundCreate, created_by: str) -> SalesOutbound:
        """创建销售出库单"""

        # 生成出库单号
        outbound_no = self._generate_outbound_no()

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == outbound_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {outbound_data.customer_id} 不存在")



        # 如果指定了销售订单，验证订单是否存在
        sales_order = None
        if outbound_data.sales_order_id:
            sales_order = self.db.query(SalesOrder).filter(SalesOrder.id == outbound_data.sales_order_id).first()
            if not sales_order:
                raise ValueError(f"销售订单ID {outbound_data.sales_order_id} 不存在")

        # 创建出库单
        db_outbound = SalesOutbound(
            outbound_no=outbound_no,
            sales_order_id=outbound_data.sales_order_id,
            sales_order_no=outbound_data.sales_order_no,
            customer_id=outbound_data.customer_id,
            status=outbound_data.status,
            outbound_date=outbound_data.outbound_date,
            created_by=created_by,
            remark=outbound_data.remark
        )

        self.db.add(db_outbound)
        self.db.flush()  # 获取出库单ID

        # 创建出库单明细
        for item_data in outbound_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 验证仓库是否存在
            warehouse = self.db.query(Warehouse).filter(Warehouse.id == item_data.warehouse_id).first()
            if not warehouse:
                raise ValueError(f"仓库ID {item_data.warehouse_id} 不存在")

            # 创建出库单明细
            db_item = SalesOutboundItem(
                outbound_id=db_outbound.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                warehouse_id=item_data.warehouse_id,
                batch_no=item_data.batch_no,
                quantity=item_data.quantity,
                outbound_quantity=0,  # 初始实出数量为0
                sales_order_line_number=item_data.sales_order_line_number  # 关联销售订单行号
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def update_sales_outbound(self, outbound_id: int, outbound_data: SalesOutboundCreate) -> Optional[SalesOutbound]:
        """更新销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 检查出库单状态，只有草稿状态的出库单才能修改
        if db_outbound.status not in ["pending"]:
            raise ValueError("只有草稿状态的出库单才能修改")

        # 验证客户是否存在
        customer = self.db.query(Customer).filter(Customer.id == outbound_data.customer_id).first()
        if not customer:
            raise ValueError(f"客户ID {outbound_data.customer_id} 不存在")



        # 更新出库单基本信息
        db_outbound.sales_order_id = outbound_data.sales_order_id
        db_outbound.sales_order_no = outbound_data.sales_order_no
        db_outbound.customer_id = outbound_data.customer_id
        db_outbound.status = outbound_data.status
        db_outbound.outbound_date = outbound_data.outbound_date
        db_outbound.remark = outbound_data.remark
        db_outbound.updated_at = datetime.now()

        # 删除原有明细
        self.db.query(SalesOutboundItem).filter(SalesOutboundItem.outbound_id == outbound_id).delete()

        # 创建新的出库单明细
        for item_data in outbound_data.items:
            # 验证商品是否存在
            product = self.db.query(Product).filter(Product.id == item_data.product_id).first()
            if not product:
                raise ValueError(f"商品ID {item_data.product_id} 不存在")

            # 验证仓库是否存在
            warehouse = self.db.query(Warehouse).filter(Warehouse.id == item_data.warehouse_id).first()
            if not warehouse:
                raise ValueError(f"仓库ID {item_data.warehouse_id} 不存在")

            # 创建出库单明细
            db_item = SalesOutboundItem(
                outbound_id=db_outbound.id,
                product_id=item_data.product_id,
                product_name=product.name,
                product_sku=product.sku,
                warehouse_id=item_data.warehouse_id,
                batch_no=item_data.batch_no,
                quantity=item_data.quantity,
                outbound_quantity=0,  # 初始实出数量为0
                sales_order_line_number=item_data.sales_order_line_number  # 关联销售订单行号
            )
            self.db.add(db_item)

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def delete_sales_outbound(self, outbound_id: int) -> bool:
        """删除销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return False

        # 检查出库单状态，只有草稿状态的出库单才能删除
        if db_outbound.status not in ["pending"]:
            raise ValueError("只有草稿状态的出库单才能删除")

        self.db.delete(db_outbound)
        self.db.commit()

        return True

    def submit_sales_outbound(self, outbound_id: int, submitted_by: str) -> Optional[SalesOutbound]:
        """提交销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能提交草稿状态的出库单
        if db_outbound.status != "pending":
            raise ValueError("只能提交草稿状态的出库单")

        # 验证出库单数据完整性
        if not db_outbound.items:
            raise ValueError("出库单必须包含商品明细")

        for item in db_outbound.items:
            if item.quantity <= 0:
                raise ValueError(f"商品 {item.product_name} 的数量必须大于0")

        # 更新状态
        db_outbound.status = "submitted"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def approve_sales_outbound(self, outbound_id: int, approved_by: str) -> Optional[SalesOutbound]:
        """审核通过销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能审核已提交状态的出库单
        if db_outbound.status != "submitted":
            raise ValueError("只能审核已提交状态的出库单")

        # 更新状态
        db_outbound.status = "approved"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def reject_sales_outbound(self, outbound_id: int, rejected_by: str, reject_reason: str = None) -> Optional[SalesOutbound]:
        """审核拒绝销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能审核已提交状态的出库单
        if db_outbound.status != "submitted":
            raise ValueError("只能审核已提交状态的出库单")

        # 更新状态
        db_outbound.status = "rejected"
        if reject_reason:
            db_outbound.remark = f"{db_outbound.remark or ''}\n拒绝原因: {reject_reason}".strip()
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def revoke_sales_outbound(self, outbound_id: int, revoked_by: str) -> Optional[SalesOutbound]:
        """撤销销售出库单（从已提交状态回到草稿状态）"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能撤销已提交状态的出库单
        if db_outbound.status != "submitted":
            raise ValueError("只能撤销已提交状态的出库单")

        # 更新状态回到草稿
        db_outbound.status = "pending"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def cancel_delivery(self, outbound_id: int, cancelled_by: str, cancel_reason: str = None) -> Optional[SalesOutbound]:
        """取消发货"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能取消已审核通过状态的出库单
        if db_outbound.status != "approved":
            raise ValueError("只能取消已审核通过状态的出库单")

        # 更新状态为已取消
        db_outbound.status = "cancelled"
        if cancel_reason:
            db_outbound.remark = f"{db_outbound.remark or ''}\n取消原因: {cancel_reason}".strip()
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def complete_sales_outbound(self, outbound_id: int, completed_by: str) -> Optional[SalesOutbound]:
        """确认完成销售出库单"""

        db_outbound = self.db.query(SalesOutbound).filter(SalesOutbound.id == outbound_id).first()
        if not db_outbound:
            return None

        # 只能完成已审核通过状态的出库单
        if db_outbound.status != "approved":
            raise ValueError("只能完成已审核通过状态的出库单")

        # 扣减库存
        from app.services.inventory_service import InventoryService
        inventory_service = InventoryService(self.db)

        for item in db_outbound.items:
            try:
                # 扣减库存
                inventory_service.reduce_stock(
                    product_id=item.product_id,
                    warehouse_id=item.warehouse_id,
                    quantity=item.quantity,
                    reason=f"销售出库单完成: {db_outbound.outbound_no}",
                    operator=completed_by,
                    batch_no=item.batch_no
                )

                # 更新出库明细的实际出库数量
                item.outbound_quantity = item.quantity

            except Exception as e:
                # 如果库存扣减失败，回滚事务
                self.db.rollback()
                raise ValueError(f"商品 {item.product_name} 扣减库存失败: {str(e)}")

        # 更新销售订单的已发货数量
        self._update_sales_order_shipped_quantity(db_outbound)

        # 更新状态为已完成
        db_outbound.status = "completed"
        db_outbound.updated_at = datetime.now()

        self.db.commit()
        self.db.refresh(db_outbound)

        return db_outbound

    def _update_sales_order_shipped_quantity(self, outbound: SalesOutbound):
        """更新销售订单的已发货数量"""

        # 如果出库单没有关联销售订单，则不需要更新
        if not outbound.sales_order_id:
            return

        # 获取销售订单明细
        order_items = self.db.query(SalesOrderItem).filter(
            SalesOrderItem.order_id == outbound.sales_order_id
        ).all()

        # 为每个出库单明细更新对应的销售订单明细的已发货数量
        for outbound_item in outbound.items:
            # 如果出库单明细有关联的销售订单行号，根据行号匹配
            if outbound_item.sales_order_line_number:
                order_item = next(
                    (item for item in order_items if item.line_number == outbound_item.sales_order_line_number),
                    None
                )
            else:
                # 如果没有关联行号，根据商品ID匹配（兼容旧数据）
                order_item = next(
                    (item for item in order_items if item.product_id == outbound_item.product_id),
                    None
                )

            if order_item:
                # 增加已发货数量
                order_item.shipped_quantity = (order_item.shipped_quantity or 0) + outbound_item.outbound_quantity

                # 确保已发货数量不超过销售数量
                if order_item.shipped_quantity > order_item.quantity:
                    order_item.shipped_quantity = order_item.quantity

                print(f"📦 更新销售订单明细 {order_item.product_name} 已发货数量: {order_item.shipped_quantity}/{order_item.quantity}")

        # 检查订单是否完全发货，如果是则更新订单状态
        self._check_and_update_sales_order_status(outbound.sales_order_id)

    def _check_and_update_sales_order_status(self, order_id: int):
        """检查并更新销售订单状态"""
        if not order_id:
            return

        # 获取销售订单
        order = self.db.query(SalesOrder).filter(
            SalesOrder.id == order_id
        ).first()

        if not order or order.status != SalesOrderStatus.APPROVED:
            return

        # 检查所有明细是否完全发货
        all_shipped = True
        for item in order.items:
            shipped_qty = item.shipped_quantity or 0
            if shipped_qty < item.quantity:
                all_shipped = False
                break

        # 如果全部发货，更新订单状态为已完成
        if all_shipped:
            order.status = SalesOrderStatus.COMPLETED
            self.db.commit()

            # 记录日志
            print(f"📦 销售订单 {order.order_no} 已完全发货，状态更新为已完成")

    def get_available_sales_orders(self, customer_id: Optional[int] = None) -> List[dict]:
        """获取可用的销售订单（已审核且有剩余未发货数量的订单）"""

        # 获取已审核状态的销售订单
        query = self.db.query(SalesOrder).options(
            joinedload(SalesOrder.customer),
            joinedload(SalesOrder.items)
        ).filter(SalesOrder.status == SalesOrderStatus.APPROVED)

        if customer_id:
            query = query.filter(SalesOrder.customer_id == customer_id)

        orders = query.order_by(desc(SalesOrder.created_at)).all()

        # 过滤出有剩余未发货数量的订单
        available_orders = []
        for order in orders:
            has_remaining = False
            remaining_items = []

            for item in order.items:
                shipped_qty = item.shipped_quantity or 0
                remaining_qty = item.quantity - shipped_qty

                if remaining_qty > 0:
                    has_remaining = True
                    # 创建一个包含剩余数量的明细副本
                    item_dict = {
                        'id': item.id,
                        'line_number': item.line_number,
                        'product_id': item.product_id,
                        'product_name': item.product_name or "",
                        'product_sku': item.product_sku or "",
                        'quantity': remaining_qty,  # 使用剩余数量
                        'original_quantity': item.quantity,  # 保留原始数量
                        'shipped_quantity': shipped_qty,
                        'unit_price': float(item.unit_price) if item.unit_price else 0.0,
                        'total_price': float(item.unit_price * remaining_qty) if item.unit_price else 0.0  # 按剩余数量计算金额
                    }
                    remaining_items.append(item_dict)

            if has_remaining:
                # 创建一个新的对象来避免修改数据库对象
                order_dict = {
                    'id': order.id,
                    'order_no': order.order_no,
                    'customer_id': order.customer_id,
                    'customer_name': order.customer.name if order.customer else "",
                    'total_amount': float(order.total_amount) if order.total_amount else 0.0,
                    'status': order.status.value if hasattr(order.status, 'value') else str(order.status),
                    'delivery_date': order.delivery_date.isoformat() if order.delivery_date else None,
                    'created_at': order.created_at.isoformat() if order.created_at else None,
                    'remaining_items': remaining_items
                }
                available_orders.append(order_dict)

        return available_orders

    def _generate_outbound_no(self) -> str:
        """生成出库单号"""
        today = datetime.now().strftime("%Y%m%d")

        # 查询今天已有的出库单数量
        count = self.db.query(SalesOutbound).filter(
            SalesOutbound.outbound_no.like(f"DO{today}%")
        ).count()

        # 生成新的出库单号
        sequence = str(count + 1).zfill(4)
        return f"DO{today}{sequence}"

    def get_sales_outbound_stats(self):
        """获取销售出库单统计"""

        # 基础统计
        total_outbounds = self.db.query(SalesOutbound).count()

        # 按状态统计 - 使用实际的状态值
        pending_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "pending").count()  # 草稿
        submitted_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "submitted").count()  # 已提交
        approved_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "approved").count()  # 已审核
        completed_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "completed").count()  # 已完成
        rejected_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "rejected").count()  # 已拒绝
        cancelled_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "cancelled").count()  # 已取消
        partial_outbounds = self.db.query(SalesOutbound).filter(SalesOutbound.status == "partial").count()  # 部分出库

        # 处理中状态（已提交+已审核）
        processing_outbounds = submitted_outbounds + approved_outbounds

        return {
            "total_outbounds": total_outbounds,
            "pending_outbounds": pending_outbounds,  # 草稿
            "submitted_outbounds": submitted_outbounds,  # 已提交
            "approved_outbounds": approved_outbounds,  # 已审核
            "processing_outbounds": processing_outbounds,  # 处理中（已提交+已审核）
            "completed_outbounds": completed_outbounds,  # 已完成
            "rejected_outbounds": rejected_outbounds,  # 已拒绝
            "cancelled_outbounds": cancelled_outbounds,  # 已取消
            "partial_outbounds": partial_outbounds  # 部分出库
        }
