# 销售发货单明细表字段添加

## 修改概述

为销售发货单明细表添加行号字段和已退货数量字段，用于后台数据管理和业务逻辑处理。这些字段不在前端展示，仅作为内部管理字段。

## 新增字段

### 1. 行号字段 (line_number)
- **类型**: INTEGER
- **用途**: 标识出库单明细的行号，便于排序和引用
- **特点**: 
  - 在同一出库单内唯一
  - 创建时自动分配（按顺序递增）
  - 前端不展示，仅用于后台管理

### 2. 已退货数量字段 (returned_quantity)
- **类型**: INTEGER
- **用途**: 跟踪该明细行的已退货数量
- **特点**:
  - 默认值为0
  - 不能为负数
  - 不能超过实出数量
  - 前端不展示，仅用于后台管理

## 修改内容

### 1. 后端数据模型修改

#### 文件：`backend/app/models/sales.py`
- **SalesOutboundItem模型**：
  - 添加 `line_number` 字段
  - 添加 `returned_quantity` 字段

### 2. 后端Schema修改

#### 文件：`backend/app/schemas/sales.py`
- **SalesOutboundItemBase**：
  - 移除 `line_number` 字段（创建时不需要，由后端自动分配）
- **SalesOutboundItemResponse**：
  - 添加 `line_number` 字段（响应中包含，标记为内部字段）
  - 添加 `returned_quantity` 字段（响应中包含，标记为内部字段）

### 3. 前端类型定义修改

#### 文件：`frontend/src/api/sales.ts`
- **SalesOutboundItem接口**：
  - 添加 `line_number?` 字段（可选，标记为后台内部字段）
  - 添加 `returned_quantity?` 字段（可选，标记为后台内部字段）

### 4. 后端服务层修改

#### 文件：`backend/app/services/sales_service.py`
- **创建出库单明细**：
  - 自动分配行号（按顺序递增）
  - 初始化已退货数量为0
- **新增方法**：
  - `update_outbound_item_returned_quantity()` - 更新已退货数量
  - `add_outbound_item_returned_quantity()` - 增加已退货数量

## 数据库迁移

### 迁移脚本：`backend/migrations/add_outbound_item_fields.sql`

```sql
-- 1. 添加行号字段
ALTER TABLE sales_outbound_items ADD COLUMN line_number INTEGER NOT NULL DEFAULT 1;

-- 2. 添加已退货数量字段
ALTER TABLE sales_outbound_items ADD COLUMN returned_quantity INTEGER NOT NULL DEFAULT 0;

-- 3. 为现有数据设置行号
UPDATE sales_outbound_items 
SET line_number = (
    SELECT row_number() OVER (PARTITION BY outbound_id ORDER BY created_at, id)
    FROM (
        SELECT id, outbound_id, created_at,
               row_number() OVER (PARTITION BY outbound_id ORDER BY created_at, id) as rn
        FROM sales_outbound_items
    ) ranked
    WHERE ranked.id = sales_outbound_items.id
);

-- 4. 创建索引和约束
CREATE INDEX idx_sales_outbound_items_line_number ON sales_outbound_items(outbound_id, line_number);
CREATE INDEX idx_sales_outbound_items_returned_qty ON sales_outbound_items(returned_quantity);

ALTER TABLE sales_outbound_items 
ADD CONSTRAINT uk_outbound_line_number 
UNIQUE (outbound_id, line_number);

ALTER TABLE sales_outbound_items 
ADD CONSTRAINT chk_returned_quantity_valid 
CHECK (returned_quantity >= 0 AND returned_quantity <= outbound_quantity);
```

## 业务逻辑

### 行号分配
- 创建出库单时，明细行按顺序自动分配行号（1, 2, 3...）
- 如果前端传入行号，使用传入的行号；否则自动分配
- 同一出库单内行号唯一

### 已退货数量管理
- 初始值为0
- 当发生退货时，通过服务方法更新对应明细的已退货数量
- 支持设置绝对值或增加相对值
- 自动验证数量的合法性

## 前端处理

### 显示策略
- **不展示**：这两个字段在前端界面中不显示
- **类型定义**：在TypeScript接口中标记为可选字段
- **注释说明**：明确标注为"后台内部字段，前端不展示"

### API响应
- 后端API响应中会包含这些字段
- 前端可以接收但不需要处理或展示
- 主要用于调试和后台管理

## 使用场景

### 1. 退货管理
- 当客户退货时，系统可以精确跟踪每个出库明细的退货情况
- 支持部分退货和多次退货
- 便于生成退货报表和统计

### 2. 数据分析
- 分析商品的退货率
- 跟踪出库和退货的对应关系
- 生成详细的库存变动报告

### 3. 业务审计
- 提供完整的出库和退货追踪链路
- 支持按行号精确定位问题
- 便于业务流程审计

## 部署步骤

1. **备份数据库**
2. **执行数据库迁移脚本**
3. **部署后端代码**
4. **部署前端代码**（可选，因为前端不展示这些字段）
5. **验证功能正常**

## 测试验证

运行测试脚本验证修改：
```bash
cd backend
python test_outbound_item_fields.py
```

## 注意事项

1. **数据一致性**：确保已退货数量不超过实出数量
2. **性能考虑**：添加了适当的索引以提高查询性能
3. **前端兼容**：前端代码无需修改，因为这些字段不展示
4. **业务逻辑**：退货时需要调用相应的服务方法更新已退货数量
5. **数据迁移**：现有数据会自动分配行号，已退货数量初始化为0

## 影响范围

- ✅ 销售出库单创建和更新
- ✅ 退货数量跟踪和管理
- ✅ 数据完整性和一致性
- ⚠️ 可能影响相关报表和统计
- ⚠️ 需要更新退货相关的业务逻辑
