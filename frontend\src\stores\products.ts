import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { productApi, type Product, type ProductAnalysisResponse } from '../api/products'

export const useProductStore = defineStore('products', () => {
  // 状态
  const products = ref<Product[]>([])
  const currentProduct = ref<Product | null>(null)
  const analysisResults = ref<ProductAnalysisResponse[]>([])
  const dashboardData = ref<any>(null)
  const loading = ref(false)
  const error = ref<string | null>(null)

  // 计算属性
  const productsByCategory = computed(() => {
    const categories: Record<string, Product[]> = {}
    products.value.forEach(product => {
      if (!categories[product.category]) {
        categories[product.category] = []
      }
      categories[product.category].push(product)
    })
    return categories
  })

  const highProfitProducts = computed(() => {
    return products.value.filter(product => 
      product.profit_margin && product.profit_margin > 30
    )
  })

  const recommendedProducts = computed(() => {
    return products.value
      .filter(product => product.recommendation_score && product.recommendation_score > 0.7)
      .sort((a, b) => (b.recommendation_score || 0) - (a.recommendation_score || 0))
  })

  // 操作方法
  const fetchProducts = async (params?: { skip?: number; limit?: number; category?: string }) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.getProducts(params)
      products.value = data.items || []
    } catch (err) {
      error.value = '获取商品列表失败'
      console.error(err)
    } finally {
      loading.value = false
    }
  }

  const fetchProduct = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.getProduct(id)
      currentProduct.value = data
      return data
    } catch (err) {
      error.value = '获取商品详情失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const createProduct = async (productData: Omit<Product, 'id' | 'created_at' | 'updated_at'>) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.createProduct(productData)
      products.value.push(data)
      return data
    } catch (err) {
      error.value = '创建商品失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const updateProduct = async (id: number, productData: Partial<Product>) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.updateProduct(id, productData)
      const index = products.value.findIndex(p => p.id === id)
      if (index !== -1) {
        products.value[index] = data
      }
      if (currentProduct.value?.id === id) {
        currentProduct.value = data
      }
      return data
    } catch (err) {
      error.value = '更新商品失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const deleteProduct = async (id: number) => {
    try {
      loading.value = true
      error.value = null
      await productApi.deleteProduct(id)
      products.value = products.value.filter(p => p.id !== id)
      if (currentProduct.value?.id === id) {
        currentProduct.value = null
      }
      return true
    } catch (err) {
      error.value = '删除商品失败'
      console.error(err)
      return false
    } finally {
      loading.value = false
    }
  }

  const analyzeProducts = async (productIds: number[], analysisType: string) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.analyzeProducts({
        product_ids: productIds,
        analysis_type: analysisType
      })
      analysisResults.value = data
      return data
    } catch (err) {
      error.value = '商品分析失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const fetchDashboard = async () => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.getDashboard()
      dashboardData.value = data
      return data
    } catch (err) {
      error.value = '获取仪表板数据失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const getRecommendations = async (params: any) => {
    try {
      loading.value = true
      error.value = null
      const data = await productApi.recommendProducts(params)
      return data
    } catch (err) {
      error.value = '获取推荐失败'
      console.error(err)
      return null
    } finally {
      loading.value = false
    }
  }

  const clearError = () => {
    error.value = null
  }

  return {
    // 状态
    products,
    currentProduct,
    analysisResults,
    dashboardData,
    loading,
    error,
    
    // 计算属性
    productsByCategory,
    highProfitProducts,
    recommendedProducts,
    
    // 方法
    fetchProducts,
    fetchProduct,
    createProduct,
    updateProduct,
    deleteProduct,
    analyzeProducts,
    fetchDashboard,
    getRecommendations,
    clearError
  }
})
